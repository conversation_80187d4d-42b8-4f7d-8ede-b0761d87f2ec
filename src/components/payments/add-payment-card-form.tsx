"use client";

import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";
import PaymentFormSkeleton from "./payment-form-skeleton";
import { addPaymentMethod, createSetupIntent, useGetPaymentMethods } from "@/api/payment-service";

const stripeElementContainerStyle =
  "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background dark:text-white focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2";

export function AddPaymentCardForm({ setOpen }: { setOpen: (open: boolean) => void }) {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [country, setCountry] = React.useState("");
  const [cardBrand, setCardBrand] = React.useState<string | null>(null);
  const [cardholderName, setCardholderName] = React.useState("");
  const [clientSecret, setClientSecret] = React.useState<string | null>(null);
  const { revalidatePaymentMethods } = useGetPaymentMethods();

  const stripeElementStyle = {
    style: {
      base: {
        fontSize: "16px",
        fontWeight: "300",
        color: "#000000",
        "::placeholder": {
          color: "#666666",
        },
        padding: "10px 0",
      },
      invalid: {
        color: "#EF4444",
      },
    },
  };

  React.useEffect(() => {
    const fetchClientSecret = async () => {
      try {
        const response = await createSetupIntent();
        const secret = response?.data?.intent?.setupIntent?.client_secret;
        if (secret) {
          setClientSecret(secret);
        }
      } catch (error) {
        console.error("Error fetching setup intent:", error);
      }
    };

    fetchClientSecret();
  }, []);

  if (!clientSecret) {
    return <PaymentFormSkeleton />;
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    try {
      if (!stripe || !elements || !country || !cardholderName.trim()) {
        setError("Please fill in all fields.");
        return;
      }

      setIsLoading(true);
      setError(null);

      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: elements.getElement(CardNumberElement)!,
        billing_details: {
          name: cardholderName.trim(),
          address: {
            country,
          },
        },
      });

      if (paymentMethodError) {
        setError(paymentMethodError.message ?? "An error occurred. Please try again.");
        setIsLoading(false);
        return;
      }

      if (!clientSecret) {
        setError("Payment setup is not initialized.");
        return;
      }

      const { error: setupError, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: paymentMethod.id,
      });

      if (setupError) {
        setError(setupError.message ?? "An error occurred while confirming card setup.");
        setIsLoading(false);
        return;
      }

      if (paymentMethod) {
        const response = await addPaymentMethod({
          paymentMethodId: paymentMethod.id,
        });

        if (response) {
          await revalidatePaymentMethods();
          toast.success("Payment method created successfully");
        }
      }

      setOpen(false);
    } catch (err) {
      setError("An error occurred while saving your card. Please try again.");
    }

    setIsLoading(false);
  }

  const handleCardChange = (event: any) => {
    setCardBrand(event.brand);
  };

  const getCardBrandIcon = (brand: string | null) => {
    switch (brand) {
      case "visa":
        return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
      case "mastercard":
        return "https://js.stripe.com/v3/fingerprinted/img/mastercard-33446994476776077777777777777777.svg";
      case "amex":
        return "https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6e418a6ca1717c.svg";
      case "discover":
        return "https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfb954e33173.svg";
      default:
        return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
    }
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm text-gray-600 dark:text-white">Cardholder Name</label>
        <input
          type="text"
          value={cardholderName}
          onChange={(e) => setCardholderName(e.target.value)}
          placeholder="Cardholder name"
          required
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm text-gray-600 dark:text-white">Card Number</label>
        <div className="relative">
          <div className={stripeElementContainerStyle}>
            <CardNumberElement
              options={stripeElementStyle}
              className="h-full w-full"
              onChange={handleCardChange}
            />
          </div>
          {cardBrand && (
            <div className="absolute top-1/2 right-3 -translate-y-1/2">
              <img
                src={getCardBrandIcon(cardBrand) || "/placeholder.svg"}
                alt={`${cardBrand} logo`}
                className="h-6 w-auto"
              />
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm text-gray-600 dark:text-white">Expiry date</label>
          <div className={stripeElementContainerStyle}>
            <CardExpiryElement
              options={stripeElementStyle}
              className="h-full w-full dark:text-white"
            />
          </div>
        </div>
        <div className="space-y-2">
          <label className="text-sm text-gray-600 dark:text-white">Security code</label>
          <div className={stripeElementContainerStyle}>
            <CardCvcElement options={stripeElementStyle} className="h-full w-full font-normal" />
          </div>
        </div>
      </div>

      {error && <div className="mt-2 text-xs text-red-500">{error}</div>}

      <button
        type="submit"
        className="w-full bg-yellow-400 font-medium text-black hover:bg-yellow-500"
        disabled={isLoading || !stripe}
      >
        {isLoading ? <Loader2 className="h-5 w-5 animate-spin" /> : "Save"}
      </button>

      <button type="button" className="w-full" onClick={() => setOpen(false)}>
        Back
      </button>
    </form>
  );
}
